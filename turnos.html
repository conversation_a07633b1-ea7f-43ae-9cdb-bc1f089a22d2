<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#0d47a1">
    <title>Calendario de Turnos de Equipo</title>
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary: #1976d2; --primary-light: #42a5f5; --primary-dark: #0d47a1;
            --secondary: #ff6d00; --secondary-light: #ff9e40;
            --background: #f0f2f5; --card: #ffffff; --text: #212121; --text-light: #757575;
            --border: rgba(0, 0, 0, 0.1); --border-radius: 12px;
            --shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            --turno-dia: #ffd600; --turno-noche: #3949ab; --turno-extra-dia: #ff9100; --turno-extra-noche: #7b1fa2;
            --turno-fijo: #4caf50; --feriado-bg: #ffcdd2; --highlight-border: #ff6d00;
        }
        .dark-mode {
            --primary: #42a5f5; --primary-light: #90caf9; --primary-dark: #1565c0;
            --secondary: #ffb74d; --secondary-light: #ffe0b2;
            --background: #121212; --card: #1e1e1e; --text: #ffffff; --text-light: #e0e0e0;
            --border: rgba(255, 255, 255, 0.15); --shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
            --turno-dia: #ffee58; --turno-noche: #7986cb; --turno-extra-dia: #ffcc80; --turno-extra-noche: #ba68c8;
            --turno-fijo: #81c784; --feriado-bg: #4a1c1e; --highlight-border: #ffb74d;
        }
        body { background-color: var(--background); color: var(--text); font-family: 'Segoe UI', system-ui, sans-serif; transition: background-color 0.3s, color 0.3s; padding-bottom: 70px; font-size: 16px; }
        .card { background-color: var(--card); border-radius: var(--border-radius); box-shadow: var(--shadow); border: 1px solid var(--border); }
        .btn-primary { background-color: var(--primary); color: white; transition: all 0.2s ease; border: none; cursor: pointer; }
        .btn-primary:hover { background-color: var(--primary-dark); transform: translateY(-2px); }
        .btn-secondary { background-color: var(--secondary); color: white; transition: all 0.2s ease; border: none; cursor: pointer; }
        .btn-secondary:hover { background-color: var(--secondary-light); transform: translateY(-2px); }
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }
        .tab.active { color: var(--primary); border-bottom-color: var(--primary); }
        .tab-content { display: none; animation: fadeIn 0.3s ease; }
        .tab-content.active { display: block; }
        @keyframes fadeIn { from { opacity: 0; } to: { opacity: 1; } }
        .day { min-height: 100px; border: 1px solid var(--border); border-radius: var(--border-radius); padding: 5px; position: relative; transition: all 0.2s ease; background-color: var(--card); cursor: pointer; }
        .day:hover { box-shadow: var(--shadow); transform: translateY(-2px); }
        .day-header { text-align: center; font-weight: bold; min-height: auto; padding: 10px 5px; background-color: var(--primary); color: white; border-radius: var(--border-radius); }
        .empty-day { background-color: transparent; border: none; box-shadow: none; pointer-events: none; }
        .today { border: 2px solid var(--primary); }
        .feriado-bg { background-color: var(--feriado-bg); }
        .day-number { font-weight: bold; font-size: 1.1rem; }
        .note-indicator { position: absolute; top: 4px; left: 4px; color: var(--primary); font-size: 0.8rem; }
        .workers-grid { display: flex; flex-direction: column; gap: 3px; margin-top: 5px; }
        .worker-badge { font-size: 0.9rem; font-weight: bold; text-align: center; border-radius: 4px; padding: 3px; color: #000; transition: transform 0.2s; }
        .worker-badge.highlight { border: 2px solid var(--highlight-border); transform: scale(1.05); }
        .badge-dia { background-color: var(--turno-dia); } .badge-noche { background-color: var(--turno-noche); color: #fff; } .badge-extra-dia { background-color: var(--turno-extra-dia); color: #fff; } .badge-extra-noche { background-color: var(--turno-extra-noche); color: #fff; } .badge-fijo { background-color: var(--turno-fijo); color: #fff; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); z-index: 1000; align-items: center; justify-content: center; }
        .modal.active { display: flex; }
        .modal-content { background-color: var(--card); padding: 1.5rem; border-radius: var(--border-radius); width: 90%; max-width: 500px; box-shadow: var(--shadow); animation: modalFadeIn 0.3s ease; }
        @keyframes modalFadeIn { from { opacity: 0; transform: scale(0.95); } to: { opacity: 1; transform: scale(1); } }
        .mobile-nav { display: none; }
        @media (max-width: 768px) { body { padding-bottom: 60px; font-size: 14px; } .tabs { display: none; } .mobile-nav { display: flex; justify-content: space-around; align-items: center; position: fixed; bottom: 0; left: 0; right: 0; background-color: var(--card); border-top: 1px solid var(--border); z-index: 100; padding: 4px 0; } .mobile-nav-item { display: flex; flex-direction: column; align-items: center; padding: 8px 4px; border-radius: 8px; color: var(--text-light); transition: all 0.2s ease; flex-grow: 1; } .mobile-nav-item.active { color: var(--primary); } .mobile-nav-item i { font-size: 1.25rem; margin-bottom: 4px; } .mobile-nav-item span { font-size: 0.7rem; } .day { min-height: 70px; } .day-number { font-size: 0.9rem; } .worker-badge { font-size: 0.7rem; } }
        select, input, textarea {
            color: var(--text);
            background-color: var(--card);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 8px;
            font-size: inherit;
        }
        .dark-mode select, .dark-mode input, .dark-mode textarea {
            color: var(--text);
            background-color: var(--card);
            border-color: var(--border);
        }

        /* Mejoras adicionales para accesibilidad */
        button:focus, input:focus, select:focus, textarea:focus {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }

        .btn-primary:disabled, .btn-secondary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        #stats-table { width: 100%; border-collapse: collapse; } #stats-table th, #stats-table td { border: 1px solid var(--border); padding: 8px; text-align: center; } #stats-table th { background-color: var(--primary); color: white; }
    </style>
</head>
<body id="body">
    <div class="container mx-auto px-2 sm:px-4 py-6 max-w-7xl">
        <header class="flex flex-col md:flex-row justify-between items-center mb-6">
            <h1 class="text-3xl md:text-4xl font-bold text-center md:text-left" style="color: var(--primary);"><i class="fas fa-users mr-2"></i>Calendario de Equipo</h1>
            <button onclick="cambiarTema()" class="btn-primary px-4 py-2 rounded-lg flex items-center text-lg mt-3 md:mt-0"><i id="theme-icon" class="fas fa-moon mr-2"></i><span>Tema</span></button>
        </header>

        <div class="card p-4">
            <nav class="tabs flex border-b border-gray-200">
                <div class="tab active" onclick="cambiarPestaña('calendario')"><i class="fas fa-calendar-alt mr-2"></i>Calendario</div>
                <div class="tab" onclick="cambiarPestaña('estadisticas')"><i class="fas fa-chart-bar mr-2"></i>Estadísticas</div>
                <div class="tab" onclick="cambiarPestaña('configuracion')"><i class="fas fa-cogs mr-2"></i>Configuración</div>
                <div class="tab" onclick="cambiarPestaña('herramientas')"><i class="fas fa-tools mr-2"></i>Herramientas</div>
            </nav>

            <div id="tab-calendario" class="tab-content active pt-4">
                <div class="card p-4 mb-4">
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-3">
                        <div class="flex-1 text-center sm:text-left"><h2 id="monthYear" class="text-2xl font-bold" style="color: var(--primary);"></h2><div id="loading-message" class="text-sm text-gray-500"></div></div>
                        <div class="flex items-center gap-2">
                            <button id="btn-generar-siguiente" onclick="generarMesSiguiente()" class="btn-secondary py-2 px-3 rounded-lg text-sm hidden"><i class="fas fa-arrow-right mr-2"></i>Generar Siguiente Mes</button>
                            <button onclick="cambiarMes(-1)" class="btn-primary p-3 rounded-lg text-lg"><i class="fas fa-chevron-left"></i></button>
                            <select id="monthSelect" onchange="actualizarCalendario()" class="rounded-lg p-2 border text-lg"></select>
                            <select id="yearSelect" onchange="actualizarCalendario()" class="rounded-lg p-2 border text-lg"></select>
                            <button onclick="cambiarMes(1)" class="btn-primary p-3 rounded-lg text-lg"><i class="fas fa-chevron-right"></i></button>
                        </div>
                    </div>
                </div>
                <div class="card p-4 mb-4">
                    <h3 class="font-semibold text-xl mb-2"><i class="fas fa-filter mr-2"></i>Filtros de Visualización</h3>
                    <div id="filtros-container" class="flex gap-4">
                        <label class="flex items-center"><input type="checkbox" id="filtro-rotativo" onchange="actualizarCalendario()" checked class="mr-2">Equipo Rotativo (1-6)</label>
                        <label class="flex items-center"><input type="checkbox" id="filtro-fijo" onchange="actualizarCalendario()" checked class="mr-2">Equipo Fijo (7-8)</label>
                    </div>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                    <div class="card p-4"><h3 class="font-semibold text-xl mb-2"><i class="fas fa-users mr-2"></i>Leyenda de Personal</h3><div id="leyenda-personal" class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2"></div></div>
                    <div class="card p-4"><h3 class="font-semibold text-xl mb-2"><i class="fas fa-palette mr-2"></i>Leyenda de Turnos</h3><div id="leyenda-turnos" class="grid grid-cols-2 md:grid-cols-3 gap-2"></div></div>
                </div>
                <div id="calendar-header" class="grid grid-cols-7 gap-1 mb-1"></div>
                <div id="calendar" class="grid grid-cols-7 gap-1"></div>
            </div>
            
            <div id="tab-estadisticas" class="tab-content pt-4"><div class="card p-4"><div class="flex justify-between items-center mb-4"><h3 class="font-semibold text-xl">Resumen de Horas del Mes</h3><button onclick="exportarCSV()" class="btn-primary py-2 px-4 rounded text-lg"><i class="fas fa-file-csv mr-2"></i>Exportar a Excel</button></div><div class="overflow-x-auto"><table id="stats-table"></table></div></div></div>
            <div id="tab-configuracion" class="tab-content pt-4"><div class="card p-4 mb-4"><h3 class="font-semibold text-xl mb-3"><i class="fas fa-user-edit mr-2"></i>Nombres del Personal</h3><div id="nombres-container" class="grid grid-cols-1 md:grid-cols-2 gap-4"></div><button onclick="guardarNombres()" class="btn-primary py-2 px-4 rounded w-full mt-4 text-lg"><i class="fas fa-save mr-2"></i>Guardar Nombres</button></div><div class="card p-4"><h3 class="font-semibold text-xl mb-3"><i class="fas fa-magic mr-2"></i>Generador Automático de Turnos 3x3</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Formato 3x3 Real: Cada equipo trabaja 9 días (3 DÍA + 6 NOCHE) y descansa 3 días. Patrón: 3 días DÍA → 3 días NOCHE → 3 días NOCHE → 3 días descanso. Los equipos A y B están desfasados 6 días para cobertura continua. Describe el turno de una persona en un día que conozcas.</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                    <div>
                        <label for="generador-fecha-referencia" class="block font-medium mb-1">1. Fecha de Referencia</label>
                        <input type="date" id="generador-fecha-referencia" class="w-full p-2 border rounded text-lg">
                    </div>
                    <div>
                        <label for="generador-equipo-referencia" class="block font-medium mb-1">2. Equipo trabajando</label>
                        <select id="generador-equipo-referencia" class="w-full p-2 border rounded text-lg">
                            <option value="A">Equipo A (1,2,3)</option>
                            <option value="B">Equipo B (4,5,6)</option>
                        </select>
                    </div>
                    <div>
                        <label for="generador-persona-dia-referencia" class="block font-medium mb-1">3. Persona de DÍA</label>
                        <select id="generador-persona-dia-referencia" class="w-full p-2 border rounded text-lg"></select>
                    </div>
                    <div>
                        <label for="generador-dia-bloque" class="block font-medium mb-1">4. Día del bloque</label>
                        <select id="generador-dia-bloque" class="w-full p-2 border rounded text-lg">
                            <option value="1">1er día de trabajo</option>
                            <option value="2">2do día de trabajo</option>
                            <option value="3">3er día de trabajo</option>
                        </select>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-4">
                    <button onclick="generarDesdeUI()" class="btn-primary py-2 px-4 rounded text-lg">
                        <i class="fas fa-sync-alt mr-2"></i>Generar Turnos para este Mes
                    </button>
                    <button onclick="validarTurnosActuales()" class="btn-secondary py-2 px-4 rounded text-lg">
                        <i class="fas fa-check-circle mr-2"></i>Validar Turnos Actuales
                    </button>
                </div>
            </div></div>
            <div id="tab-herramientas" class="tab-content pt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card p-4">
                        <h3 class="font-semibold text-xl mb-3"><i class="fas fa-file-pdf mr-2"></i>Exportar Calendario a PDF</h3>
                        <div class="grid grid-cols-1 gap-3">
                            <div>
                                <label for="export-start-month" class="block font-medium mb-1">Desde el Mes:</label>
                                <input type="month" id="export-start-month" class="w-full p-2 rounded border text-lg">
                            </div>
                            <div>
                                <label for="export-end-month" class="block font-medium mb-1">Hasta el Mes:</label>
                                <input type="month" id="export-end-month" class="w-full p-2 rounded border text-lg">
                            </div>
                            <div>
                                <label for="exportarPersonaSelect" class="block font-medium mb-1">Seleccionar Persona:</label>
                                <select id="exportarPersonaSelect" class="w-full p-2 rounded border text-lg"></select>
                            </div>
                            <button onclick="exportarAPDF()" class="btn-secondary py-2 px-4 rounded w-full text-lg">
                                <i class="fas fa-file-pdf mr-2"></i>Exportar a PDF
                            </button>
                        </div>
                    </div>
                    <div class="card p-4">
                        <h3 class="font-semibold text-xl mb-3"><i class="fas fa-database mr-2"></i>Gestión de Datos</h3>
                        <div class="grid grid-cols-1 gap-3">
                            <button onclick="importarDatosJSON()" class="btn-primary py-2 rounded text-lg">
                                <i class="fas fa-file-upload mr-2"></i>Importar Respaldo
                            </button>
                            <button onclick="exportarDatosJSON()" class="btn-primary py-2 rounded text-lg">
                                <i class="fas fa-file-download mr-2"></i>Exportar Respaldo
                            </button>
                            <button onclick="limpiarMes()" class="bg-yellow-500 hover:bg-yellow-600 text-black py-2 rounded text-lg">
                                <i class="fas fa-trash-alt mr-2"></i>Limpiar Mes Actual
                            </button>
                            <button onclick="limpiarTodosLosDatos()" class="bg-red-700 hover:bg-red-800 text-white py-2 rounded text-lg">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Borrar Todos los Turnos
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="daySummaryModal" class="modal"><div class="modal-content"><h3 id="summaryModalTitle" class="text-2xl font-bold mb-4"></h3><div id="summaryModalContent" class="mb-4"></div><div class="mb-4"><label class="block font-medium mb-1">Nota General del Día:</label><textarea id="notaGeneral" rows="3" class="w-full p-2 rounded border"></textarea></div><div class="flex justify-between flex-wrap gap-2"><button onclick="guardarNotaGeneral()" class="btn-primary px-4 py-2 rounded">Guardar Nota</button><button id="editDayButton" class="btn-secondary px-4 py-2 rounded">Editar Turnos</button><button onclick="cerrarModal('daySummaryModal')" class="bg-gray-300 dark:bg-gray-700 px-4 py-2 rounded">Cerrar</button></div></div></div>
    <div id="turnoModal" class="modal">
        <div class="modal-content">
            <h3 id="modalTitle" class="text-2xl font-bold mb-4"></h3>
            <div id="edit-shifts-container" class="space-y-3"></div>
            <hr class="my-4">
            <div id="add-shift-container">
                <h4 class="font-semibold text-lg mb-2">Añadir Persona al Turno</h4>
                <div class="flex gap-2">
                    <select id="add-persona-select" class="w-full p-2 border rounded"></select>
                    <select id="add-turno-select" class="w-full p-2 border rounded"></select>
                    <button onclick="añadirTurno()" class="btn-primary px-4 py-2 rounded">Añadir</button>
                </div>
            </div>
             <button onclick="cerrarModal('turnoModal')" class="bg-gray-300 dark:bg-gray-700 px-4 py-2 rounded w-full mt-4">Cerrar</button>
        </div>
    </div>
    
    <nav class="mobile-nav">
        <div class="mobile-nav-item active" onclick="cambiarPestaña('calendario')"><i class="fas fa-calendar-alt"></i><span>Calendario</span></div><div class="mobile-nav-item" onclick="cambiarPestaña('estadisticas')"><i class="fas fa-chart-bar"></i><span>Stats</span></div><div class="mobile-nav-item" onclick="cambiarPestaña('configuracion')"><i class="fas fa-cogs"></i><span>Config</span></div><div class="mobile-nav-item" onclick="cambiarPestaña('herramientas')"><i class="fas fa-tools"></i><span>PDF/Datos</span></div>
    </nav>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.8.2/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ics@3.7.2/dist/ics.min.js"></script>

    <script>
        // --- VARIABLES GLOBALES ---
        let turnos, nombres, notasGenerales, lastGeneratorParams;

        // Función para cargar datos de localStorage de forma segura
        function cargarDatosLocalStorage() {
            try {
                turnos = JSON.parse(localStorage.getItem('turnos_equipo')) || {};
                nombres = JSON.parse(localStorage.getItem('nombres_personas')) || {};
                notasGenerales = JSON.parse(localStorage.getItem('notas_generales')) || {};
                lastGeneratorParams = JSON.parse(localStorage.getItem('last_generator_params')) || null;
            } catch (error) {
                console.error('Error cargando datos del localStorage:', error);
                turnos = {};
                nombres = {};
                notasGenerales = {};
                lastGeneratorParams = null;
                alert('Hubo un problema cargando los datos guardados. Se iniciará con datos vacíos.');
            }
        }
        let fechaActual = new Date();
        let diaSeleccionado, mesSeleccionado, añoSeleccionado, personaResaltada = null;
        const N_PERSONAS = 8;
        let feriadosCache = {};
        let allHolidaysCache = null;
        const TURNO_DEFINICIONES = { 'dia': { inicial: 'D', color: '#ffd600', texto: '#000000' }, 'noche': { inicial: 'N', color: '#3949ab', texto: '#ffffff' }, 'extra-dia': { inicial: 'ED', color: '#ff9100', texto: '#ffffff' }, 'extra-noche': { inicial: 'EN', color: '#7b1fa2', texto: '#ffffff' }, 'fijo-1': { inicial: 'F1', color: '#4caf50', texto: '#ffffff' }, 'fijo-2': { inicial: 'F2', color: '#4caf50', texto: '#ffffff' } };

        // --- LÓGICA DE FERIADOS (CON FALLBACK) ---
        function calcularFeriadosLocales(año) { const feriados = new Map(); const feriadosFijos = { '01-01': 'Año Nuevo', '05-01': 'Día del Trabajador', '05-21': 'Día de las Glorias Navales', '06-29': 'San Pedro y San Pablo', '07-16': 'Día de la Virgen del Carmen', '08-15': 'Asunción de la Virgen', '09-18': 'Independencia Nacional', '09-19': 'Día de las Glorias del Ejército', '10-12': 'Encuentro de Dos Mundos', '10-31': 'Día de las Iglesias Evangélicas', '11-01': 'Día de Todos los Santos', '12-08': 'Inmaculada Concepción', '12-25': 'Navidad' }; for (const [fecha, nombre] of Object.entries(feriadosFijos)) { const [mes, dia] = fecha.split('-').map(Number); feriados.set(formatDateForAPI(new Date(Date.UTC(año, mes - 1, dia))), nombre); } return feriados; }
        async function getFeriados(año) {
            if (feriadosCache[año]) return feriadosCache[año];
            const loadingMsg = document.getElementById('loading-message');
            loadingMsg.textContent = 'Cargando feriados...';
            try {
                if (!allHolidaysCache) { const response = await fetch('https://api.boostr.cl/holidays.json'); if (!response.ok) throw new Error('Respuesta de red no fue exitosa'); const data = await response.json(); allHolidaysCache = data.data; }
                const feriadosMap = new Map();
                allHolidaysCache.filter(f => f.date.startsWith(String(año))).forEach(f => feriadosMap.set(f.date, f.title));
                feriadosCache[año] = feriadosMap;
                loadingMsg.textContent = '';
                return feriadosMap;
            } catch (error) {
                loadingMsg.textContent = '';
                alert('No se pudieron cargar los feriados desde internet. Se usará una lista de feriados local. Algunos feriados nuevos podrían no aparecer.');
                console.error("Error cargando feriados:", error);
                feriadosCache[año] = calcularFeriadosLocales(año);
                return feriadosCache[año];
            }
        }
        const formatDateForAPI = (date) => date.toISOString().split('T')[0];

        // --- CALENDARIO Y UI ---
        async function actualizarCalendario() {
            mesSeleccionado = parseInt(document.getElementById('monthSelect').value); añoSeleccionado = parseInt(document.getElementById('yearSelect').value);
            fechaActual = new Date(añoSeleccionado, mesSeleccionado, 1);
            document.getElementById('monthYear').textContent = fechaActual.toLocaleDateString('es-CL', { month: 'long', year: 'numeric' }).replace(/^\w/, c => c.toUpperCase());
            document.getElementById('generador-fecha-referencia').value = formatDateForAPI(new Date(Date.UTC(añoSeleccionado, mesSeleccionado, 1)));
            const feriadosDelAño = await getFeriados(añoSeleccionado);
            const calendar = document.getElementById('calendar'); calendar.innerHTML = '';
            const diasMes = new Date(añoSeleccionado, mesSeleccionado + 1, 0).getDate(); const primerDiaSemana = (new Date(añoSeleccionado, mesSeleccionado, 1).getDay() + 6) % 7;
            const filtroRotativo = document.getElementById('filtro-rotativo').checked; const filtroFijo = document.getElementById('filtro-fijo').checked;
            for (let i = 0; i < primerDiaSemana; i++) { calendar.innerHTML += `<div class="day empty-day"></div>`; }
            for (let dia = 1; dia <= diasMes; dia++) {
                const fecha = new Date(Date.UTC(añoSeleccionado, mesSeleccionado, dia)); const fechaStr = formatDateForAPI(fecha); const esFeriado = feriadosDelAño.has(fechaStr); const esHoy = formatDateForAPI(new Date()) === fechaStr;
                const esDomingo = fecha.getUTCDay() === 0;
                const divDia = document.createElement('div'); divDia.className = `day ${esHoy ? 'today' : ''} ${(esFeriado || esDomingo) ? 'feriado-bg' : ''}`; divDia.onclick = () => abrirResumenDia(dia);
                const turnosDia = turnos[añoSeleccionado]?.[mesSeleccionado]?.[dia] || {};
                let trabajadoresDelDia = Object.entries(turnosDia).map(([id, data]) => ({ id: parseInt(id), turnoData: data }));
                trabajadoresDelDia = trabajadoresDelDia.filter(t => (t.id <= 6 && filtroRotativo) || (t.id > 6 && filtroFijo));
                trabajadoresDelDia.sort((a, b) => { const orden = { 'dia': 1, 'extra-dia': 1, 'noche': 2, 'extra-noche': 2, 'fijo-1': 3, 'fijo-2': 3 }; const ordenA = orden[a.turnoData.turno] || 4; const ordenB = orden[b.turnoData.turno] || 4; if (ordenA !== ordenB) return ordenA - ordenB; return a.id - b.id; });
                let workersHTML = '<div class="workers-grid">';
                trabajadoresDelDia.forEach(({id, turnoData}) => { const inicial = TURNO_DEFINICIONES[turnoData.turno]?.inicial || '?'; const esResaltado = personaResaltada == id ? 'highlight' : ''; workersHTML += `<div class="worker-badge badge-${turnoData.turno.replace(/-\d/,'')} ${esResaltado}">${id}(${inicial})</div>`; });
                workersHTML += '</div>';
                const notaIcon = notasGenerales[fechaStr] ? `<i class="fas fa-comment-alt note-indicator" title="Hay una nota este día"></i>` : '';
                divDia.innerHTML = `<div class="day-number">${dia}</div>${notaIcon}${workersHTML}`;
                calendar.appendChild(divDia);
            }
            document.getElementById('btn-generar-siguiente').style.display = lastGeneratorParams ? 'block' : 'none';
        }

        // --- GESTIÓN DE CONFIGURACIONES Y LEYENDAS ---
        function cargarNombres() { const container = document.getElementById('nombres-container'); container.innerHTML = ''; for (let i = 1; i <= N_PERSONAS; i++) { container.innerHTML += `<div><label for="nombre-persona-${i}" class="block font-medium mb-1">Persona ${i}</label><input type="text" id="nombre-persona-${i}" value="${nombres[i] || ''}" class="w-full p-2 rounded border text-lg"></div>`; } }
        function guardarNombres() { for (let i = 1; i <= N_PERSONAS; i++) { nombres[i] = document.getElementById(`nombre-persona-${i}`).value.trim(); } localStorage.setItem('nombres_personas', JSON.stringify(nombres)); actualizarLeyendas(); actualizarSelectoresDePersona(); document.getElementById('generador-equipo-referencia').dispatchEvent(new Event('change')); alert('Nombres guardados.'); }
        function actualizarLeyendas() {
            const pContainer = document.getElementById('leyenda-personal'); pContainer.innerHTML = '';
            const horariosFijosDesc = { 7: `(L-J: 07:30-16:00, V: 07:30-15:00)`, 8: `(L-J: 11:00-20:00, V: 12:00-20:00)` };
            for (let i = 1; i <= N_PERSONAS; i++) {
                let detalle = '(Turno Rotativo 3x3)'; if (i === 7 || i === 8) detalle = horariosFijosDesc[i];
                pContainer.innerHTML += `<div class="p-1 flex items-center justify-between"><div class="cursor-pointer hover:font-bold" onclick="resaltarPersona(${i})"><b>${i}:</b> ${nombres[i] || 'No asignado'} <span class="text-sm text-gray-500">${detalle}</span></div><i class="fas fa-calendar-plus ml-2 cursor-pointer text-blue-500" title="Exportar horario a calendario personal" onclick="exportarICS(${i})"></i></div>`;
            }
            const tContainer = document.getElementById('leyenda-turnos');
            tContainer.innerHTML = Object.entries(TURNO_DEFINICIONES).map(([key, val]) => `<div class="flex items-center"><div class="w-4 h-4 rounded-full mr-2" style="background-color:${val.color};"></div>${val.inicial}: ${key.replace('-',' ').replace('fijo 1','Fijo 7').replace('fijo 2','Fijo 8')}</div>`).join('');
        }
        function resaltarPersona(id) { personaResaltada = (personaResaltada === id) ? null : id; actualizarCalendario(); }
        function actualizarSelectoresDePersona() { const selExport = document.getElementById('exportarPersonaSelect'); const oldValue = selExport.value; selExport.innerHTML = '<option value="all">Todas las Personas</option>'; for (let i = 1; i <= N_PERSONAS; i++) { selExport.innerHTML += `<option value="${i}">${nombres[i] || `Persona ${i}`}</option>`; } selExport.value = oldValue; }

        // --- MODALES (RESUMEN Y EDICIÓN) ---
        function abrirResumenDia(dia) {
            diaSeleccionado = dia; const fecha = new Date(Date.UTC(añoSeleccionado, mesSeleccionado, dia)); const fechaStr = formatDateForAPI(fecha);
            document.getElementById('summaryModalTitle').textContent = `Resumen del ${fecha.toLocaleDateString('es-CL', {weekday: 'long', day: 'numeric', month: 'long', timeZone: 'UTC'})}`;
            const turnosDia = turnos[añoSeleccionado]?.[mesSeleccionado]?.[dia] || {}; let content = '<ul>';
            Object.entries(turnosDia).forEach(([id, data]) => { content += `<li class="mb-1"><b>${nombres[id] || `P. ${id}`}:</b> ${data.turno.replace('-',' ')}</li>`; });
            document.getElementById('summaryModalContent').innerHTML = content.length > 4 ? content + '</ul>' : '<p>No hay turnos asignados para este día.</p>';
            document.getElementById('notaGeneral').value = notasGenerales[fechaStr] || '';
            document.getElementById('editDayButton').dataset.dia = dia;
            document.getElementById('daySummaryModal').classList.add('active');
        }
        function abrirModal(dia) {
            diaSeleccionado = dia;
            document.getElementById('modalTitle').textContent = `Editar Turnos - ${dia}/${mesSeleccionado + 1}`;
            const turnosDia = turnos[añoSeleccionado]?.[mesSeleccionado]?.[dia] || {};
            const editContainer = document.getElementById('edit-shifts-container');
            editContainer.innerHTML = '';
            const personasTrabajando = Object.keys(turnosDia).map(id => parseInt(id));
            if (personasTrabajando.length === 0) { editContainer.innerHTML = '<p>No hay turnos asignados para editar.</p>'; } 
            else {
                personasTrabajando.forEach(id => {
                    const turnoActual = turnosDia[id].turno;
                    let optionsHTML = document.getElementById('add-turno-select').innerHTML;
                    editContainer.innerHTML += `<div class="flex items-center gap-2 mb-2"><span class="font-bold flex-1">${nombres[id] || `Persona ${id}`}</span><select id="shift-select-person-${id}" data-person-id="${id}" class="w-full p-2 border rounded flex-1">${optionsHTML}</select><button onclick="quitarTurno(${id})" class="bg-red-500 text-white p-2 rounded" title="Quitar Turno"><i class="fas fa-trash"></i></button></div>`;
                    setTimeout(() => { document.getElementById(`shift-select-person-${id}`).value = turnoActual; }, 0);
                });
            }
            const addPersonaSelect = document.getElementById('add-persona-select');
            addPersonaSelect.innerHTML = '';
            for (let i = 1; i <= N_PERSONAS; i++) { if (!personasTrabajando.includes(i)) { addPersonaSelect.innerHTML += `<option value="${i}">${nombres[i] || `Persona ${i}`}</option>`; } }
            if (addPersonaSelect.innerHTML === '') { addPersonaSelect.innerHTML = '<option value="">Nadie para añadir</option>'; }
            document.getElementById('turnoModal').classList.add('active');
        }
        function cerrarModal(modalId) { document.getElementById(modalId).classList.remove('active'); }
        document.getElementById('edit-shifts-container').addEventListener('change', function(event) { if (event.target.tagName === 'SELECT') { const personaId = event.target.dataset.personId; const nuevoTurno = event.target.value; guardarTurno(personaId, nuevoTurno); } });
        function guardarTurno(personaId, turno, nota = '') { if (turno === 'libre') { quitarTurno(personaId); return; } if (!turnos[añoSeleccionado]) turnos[añoSeleccionado] = {}; if (!turnos[añoSeleccionado][mesSeleccionado]) turnos[añoSeleccionado][mesSeleccionado] = {}; if (!turnos[añoSeleccionado][mesSeleccionado][diaSeleccionado]) turnos[añoSeleccionado][mesSeleccionado][diaSeleccionado] = {}; turnos[añoSeleccionado][mesSeleccionado][diaSeleccionado][personaId] = { turno, nota }; guardarDatos(); actualizarCalendario(); }
        function quitarTurno(personaId) { if (turnos[añoSeleccionado]?.[mesSeleccionado]?.[diaSeleccionado]?.[personaId]) { delete turnos[añoSeleccionado][mesSeleccionado][diaSeleccionado][personaId]; if (Object.keys(turnos[añoSeleccionado][mesSeleccionado][diaSeleccionado]).length === 0) { delete turnos[añoSeleccionado][mesSeleccionado][diaSeleccionado]; } guardarDatos(); actualizarCalendario(); abrirModal(diaSeleccionado); } }
        function añadirTurno() { const personaId = document.getElementById('add-persona-select').value; const turno = document.getElementById('add-turno-select').value; if (!personaId || turno === 'libre') return; guardarTurno(personaId, turno); abrirModal(diaSeleccionado); }
        function guardarNotaGeneral() { const fechaStr = formatDateForAPI(new Date(Date.UTC(añoSeleccionado, mesSeleccionado, diaSeleccionado))); const nota = document.getElementById('notaGeneral').value; if (nota.trim()) { notasGenerales[fechaStr] = nota.trim(); } else { delete notasGenerales[fechaStr]; } localStorage.setItem('notas_generales', JSON.stringify(notasGenerales)); actualizarCalendario(); cerrarModal('daySummaryModal'); }

        // --- GENERADOR AUTOMÁTICO (CORREGIDO) ---
        function generarDesdeUI() {
            const params = {
                fechaRefStr: document.getElementById('generador-fecha-referencia').value,
                equipoRef: document.getElementById('generador-equipo-referencia').value,
                personaDiaRef: parseInt(document.getElementById('generador-persona-dia-referencia').value),
                diaDelBloqueRef: parseInt(document.getElementById('generador-dia-bloque').value),
                año: añoSeleccionado,
                mes: mesSeleccionado
            };
            if (!params.fechaRefStr || !params.personaDiaRef || !params.diaDelBloqueRef) { 
                return alert('Por favor, completa los cuatro campos del generador.'); 
            }
            if (!confirm(`¿Generar los turnos para ${new Date(params.año, params.mes).toLocaleDateString('es-CL', {month: 'long'})}?`)) {
                return;
            }
            
            generarCalendarioCompleto(params);
        }

        async function generarCalendarioCompleto(params) {
            const fechaReferencia = new Date(params.fechaRefStr + 'T12:00:00.000Z');
            const equipoA = [1, 2, 3], equipoB = [4, 5, 6];
            const equipoPersona = equipoA.includes(params.personaDiaRef) ? 'A' : 'B';

            if(equipoPersona !== params.equipoRef) {
                return alert(`La persona seleccionada no pertenece al equipo de referencia.`);
            }

            // Calcular el inicio del bloque de trabajo de referencia
            const fechaInicioBloqueRef = new Date(fechaReferencia);
            fechaInicioBloqueRef.setUTCDate(fechaReferencia.getUTCDate() - (params.diaDelBloqueRef - 1));

            const indiceRotacionRef = (params.personaDiaRef - 1) % 3;

            const diasMes = new Date(params.año, params.mes + 1, 0).getDate();
            const feriadosDelAño = await getFeriados(params.año);

            if(!turnos[params.año]) turnos[params.año] = {};
            turnos[params.año][params.mes] = {}; // Limpiamos el mes actual

            console.log('Generando turnos 3x3 con parámetros:', {
                fechaReferencia: fechaReferencia.toISOString().split('T')[0],
                fechaInicioBloqueRef: fechaInicioBloqueRef.toISOString().split('T')[0],
                equipoRef: params.equipoRef,
                personaDiaRef: params.personaDiaRef,
                diaDelBloqueRef: params.diaDelBloqueRef
            });

            for (let dia = 1; dia <= diasMes; dia++) {
                const fecha = new Date(Date.UTC(params.año, params.mes, dia, 12, 0, 0));

                // Calcular días transcurridos desde el inicio del bloque de referencia
                const diffDias = Math.round((fecha.getTime() - fechaInicioBloqueRef.getTime()) / (1000 * 60 * 60 * 24));

                // Ciclo 3x3 CORRECTO: 12 días por ciclo completo
                // Cada equipo: 3 días DÍA → 3 días NOCHE → 3 días NOCHE → 3 días descanso
                // Los equipos están desfasados 6 días para cobertura continua

                const posicionEnCiclo12 = ((diffDias % 12) + 12) % 12;

                // Determinar qué equipo trabaja y en qué modalidad
                let equipoTrabajando = null;
                let tipoTurno = null;

                // Patrón para Equipo A (basado en el equipo de referencia)
                const equipoAPos = posicionEnCiclo12;
                // Patrón para Equipo B (desfasado 6 días)
                const equipoBPos = (posicionEnCiclo12 + 6) % 12;

                // Función para determinar el estado según posición en ciclo de 12 días
                function getEstadoEquipo(pos) {
                    if (pos >= 0 && pos <= 2) return 'dia';      // 3 días DÍA
                    if (pos >= 3 && pos <= 8) return 'noche';    // 6 días NOCHE (3+3)
                    if (pos >= 9 && pos <= 11) return 'descanso'; // 3 días descanso
                }

                const estadoA = getEstadoEquipo(equipoAPos);
                const estadoB = getEstadoEquipo(equipoBPos);

                // Asignar el equipo que está trabajando
                if (estadoA !== 'descanso') {
                    equipoTrabajando = 'A';
                    tipoTurno = estadoA;
                } else if (estadoB !== 'descanso') {
                    equipoTrabajando = 'B';
                    tipoTurno = estadoB;
                }

                let personasAsignadas = [];

                if (equipoTrabajando && tipoTurno) {
                    const equipo = equipoTrabajando === 'A' ? equipoA : equipoB;

                    // Determinar rotación dentro del equipo (cada 3 días cambia quién hace día/noche)
                    const cicloRotacion = Math.floor(diffDias / 3);
                    const indicePersona = ((indiceRotacionRef + cicloRotacion) % 3 + 3) % 3;

                    if (tipoTurno === 'dia') {
                        // Turno de DÍA: 1 persona día + 2 personas noche
                        const pDia = equipo[indicePersona];
                        const pNoche1 = equipo[(indicePersona + 1) % 3];
                        const pNoche2 = equipo[(indicePersona + 2) % 3];

                        asignarTurno(params.año, params.mes, dia, pDia, 'dia');
                        asignarTurno(params.año, params.mes, dia, pNoche1, 'noche');
                        asignarTurno(params.año, params.mes, dia, pNoche2, 'noche');

                        personasAsignadas = [`${equipoTrabajando}-Día: P${pDia}`, `${equipoTrabajando}-Noche: P${pNoche1}, P${pNoche2}`];
                    } else if (tipoTurno === 'noche') {
                        // Turno de NOCHE: 3 personas noche
                        const pNoche1 = equipo[indicePersona];
                        const pNoche2 = equipo[(indicePersona + 1) % 3];
                        const pNoche3 = equipo[(indicePersona + 2) % 3];

                        asignarTurno(params.año, params.mes, dia, pNoche1, 'noche');
                        asignarTurno(params.año, params.mes, dia, pNoche2, 'noche');
                        asignarTurno(params.año, params.mes, dia, pNoche3, 'noche');

                        personasAsignadas = [`${equipoTrabajando}-Noche: P${pNoche1}, P${pNoche2}, P${pNoche3}`];
                    }

                    const equipoDescansando = equipoTrabajando === 'A' ? 'B' : 'A';
                    console.log(`Día ${dia}: Pos${posicionEnCiclo12} - Equipo ${equipoTrabajando} (${tipoTurno}), Equipo ${equipoDescansando} (${estadoA === 'descanso' ? 'descanso' : estadoB === 'descanso' ? 'descanso' : 'trabajando'}) - ${personasAsignadas.join(', ')}`);
                } else {
                    console.log(`Día ${dia}: Pos${posicionEnCiclo12} - Ambos equipos en descanso (A: ${estadoA}, B: ${estadoB})`);
                }

                // Asignar turnos fijos (lunes a viernes, no feriados)
                if (fecha.getUTCDay() >= 1 && fecha.getUTCDay() <= 5 && !feriadosDelAño.has(formatDateForAPI(fecha))) {
                    asignarTurno(params.año, params.mes, dia, 7, 'fijo-1');
                    asignarTurno(params.año, params.mes, dia, 8, 'fijo-2');
                }
            }
            localStorage.setItem('last_generator_params', JSON.stringify(params));
            guardarDatos();
            await actualizarCalendario();

            // Validar la secuencia generada
            const validacion = validarSecuenciaTurnos3x3(params.año, params.mes);
            let mensaje = `Calendario 3x3 generado para ${new Date(params.año, params.mes).toLocaleDateString('es-CL', {month: 'long', year: 'numeric'})}.\n\n`;
            mensaje += `Días con turnos: ${validacion.diasConTurnos}\n`;
            mensaje += `Días sin turnos: ${validacion.diasSinTurnos}\n`;

            if (validacion.errores.length > 0) {
                mensaje += `\n⚠️ Se encontraron ${validacion.errores.length} errores. Revisa la consola para más detalles.`;
            } else {
                mensaje += '\n✅ Secuencia de turnos 3x3 generada correctamente.';
            }

            alert(mensaje);
        }

        async function generarMesSiguiente3x3() {
            let lastParams = JSON.parse(localStorage.getItem('last_generator_params'));
            if (!lastParams) {
                return alert("Primero debes generar un mes desde la pestaña 'Configuración' para poder continuar la secuencia.");
            }

            // Calcular el primer día del mes siguiente
            const siguienteMesDate = new Date(Date.UTC(lastParams.año, lastParams.mes + 1, 1, 12, 0, 0));

            // Recalcular la fecha de inicio del bloque de referencia original
            const fechaRefOriginal = new Date(lastParams.fechaRefStr + 'T12:00:00.000Z');
            const fechaInicioBloqueOriginal = new Date(fechaRefOriginal);
            fechaInicioBloqueOriginal.setUTCDate(fechaRefOriginal.getUTCDate() - (lastParams.diaDelBloqueRef - 1));

            // Calcular días transcurridos desde el inicio del bloque original hasta el primer día del mes siguiente
            const diffDias = Math.round((siguienteMesDate.getTime() - fechaInicioBloqueOriginal.getTime()) / (1000 * 60 * 60 * 24));

            // En el formato 3x3 REAL, el ciclo es de 12 días
            const posicionEnCiclo12 = ((diffDias % 12) + 12) % 12;

            // Calcular cuántos ciclos completos de 12 días han pasado
            const numCiclosCompletos = Math.floor(diffDias / 12);

            const equipoA = [1, 2, 3], equipoB = [4, 5, 6];
            const equipoOriginal = lastParams.equipoRef;

            // Mantenemos el equipo de referencia
            const nuevoEquipoRef = equipoOriginal;
            const equipoMiembros = nuevoEquipoRef === 'A' ? equipoA : equipoB;

            // Calcular la rotación de personas dentro del equipo (cada 3 días cambia)
            const indiceRotacionOriginal = (lastParams.personaDiaRef - 1) % 3;
            const ciclosRotacion = Math.floor(diffDias / 3); // Cada 3 días rota
            const nuevaPersonaDia = equipoMiembros[(indiceRotacionOriginal + ciclosRotacion) % 3];

            // Determinar en qué día del bloque de 3 días estamos
            let nuevoDiaDelBloqueRef;
            if (posicionEnCiclo12 >= 0 && posicionEnCiclo12 <= 2) {
                // Días de DÍA (0,1,2)
                nuevoDiaDelBloqueRef = (posicionEnCiclo12 % 3) + 1;
            } else if (posicionEnCiclo12 >= 6 && posicionEnCiclo12 <= 8) {
                // Días de NOCHE (6,7,8) - tratamos como días de trabajo
                nuevoDiaDelBloqueRef = ((posicionEnCiclo12 - 6) % 3) + 1;
            } else {
                // Días de descanso - empezamos nuevo bloque
                nuevoDiaDelBloqueRef = 1;
            }

            const nextMonthParams = {
                fechaRefStr: siguienteMesDate.toISOString().split('T')[0],
                equipoRef: nuevoEquipoRef,
                personaDiaRef: nuevaPersonaDia,
                diaDelBloqueRef: nuevoDiaDelBloqueRef,
                año: siguienteMesDate.getUTCFullYear(),
                mes: siguienteMesDate.getUTCMonth()
            };

            console.log('Generando mes siguiente 3x3 REAL con parámetros:', nextMonthParams);
            console.log(`Posición en ciclo 12: ${posicionEnCiclo12}, Ciclos completos: ${numCiclosCompletos}`);

            document.getElementById('monthSelect').value = nextMonthParams.mes;
            document.getElementById('yearSelect').value = nextMonthParams.año;

            await generarCalendarioCompleto(nextMonthParams);
        }

        function asignarTurno(año, mes, dia, pId, t, n = '') {
            if (!turnos[año]) turnos[año] = {};
            if (!turnos[año][mes]) turnos[año][mes] = {};
            if (!turnos[año][mes][dia]) turnos[año][mes][dia] = {};
            turnos[año][mes][dia][pId] = { turno: t, nota: n };
        }

        // Función para validar la secuencia de turnos 3x3 REAL (patrón de 12 días)
        function validarSecuenciaTurnos3x3(año, mes) {
            const diasMes = new Date(año, mes + 1, 0).getDate();
            let errores = [];
            let diasConTurnos = 0;
            let diasSinTurnos = 0;

            for (let dia = 1; dia <= diasMes; dia++) {
                const turnosDia = turnos[año]?.[mes]?.[dia] || {};
                const personasRotativas = Object.keys(turnosDia).filter(id => parseInt(id) <= 6);

                if (personasRotativas.length > 0) {
                    diasConTurnos++;

                    // En el patrón 3x3 REAL, solo un equipo trabaja a la vez:
                    // - Equipo en turno DÍA: 1 persona día + 2 personas noche = 3 personas
                    // - Equipo en turno NOCHE: 3 personas noche = 3 personas
                    // - El otro equipo está completamente descansando

                    const personasDia = personasRotativas.filter(id => turnosDia[id].turno === 'dia');
                    const personasNoche = personasRotativas.filter(id => turnosDia[id].turno === 'noche');

                    // Verificar configuraciones válidas
                    const totalPersonas = personasRotativas.length;
                    const esDia = personasDia.length;
                    const esNoche = personasNoche.length;

                    let configuracionValida = false;

                    // Configuración 1: Un equipo en turno DÍA (1 día + 2 noche = 3 personas)
                    if (esDia === 1 && esNoche === 2 && totalPersonas === 3) {
                        configuracionValida = true;
                    }
                    // Configuración 2: Un equipo en turno NOCHE (3 noche = 3 personas)
                    else if (esDia === 0 && esNoche === 3 && totalPersonas === 3) {
                        configuracionValida = true;
                    }

                    if (!configuracionValida) {
                        errores.push(`Día ${dia}: Configuración inválida - ${esDia} día, ${esNoche} noche (total: ${totalPersonas}). Esperado: 3 personas de un solo equipo`);
                    }

                    // Verificar que todas las personas sean del mismo equipo
                    const equipoA = personasRotativas.filter(id => parseInt(id) <= 3);
                    const equipoB = personasRotativas.filter(id => parseInt(id) >= 4 && parseInt(id) <= 6);

                    if (equipoA.length > 0 && equipoB.length > 0) {
                        errores.push(`Día ${dia}: Ambos equipos trabajando - Equipo A: ${equipoA.length}, Equipo B: ${equipoB.length}. Solo un equipo debe trabajar`);
                    } else if (equipoA.length !== 3 && equipoB.length !== 3) {
                        errores.push(`Día ${dia}: Número incorrecto de personas - Equipo A: ${equipoA.length}, Equipo B: ${equipoB.length}. Esperado: 3 de un equipo, 0 del otro`);
                    }

                } else {
                    diasSinTurnos++;
                    // En el patrón 3x3 real, es normal tener días sin turnos (días de descanso)
                    // No es un error, pero lo registramos
                }
            }

            console.log(`Validación de turnos 3x3 REAL (ciclo 12 días) ${mes+1}/${año}:`);
            console.log(`- Días con turnos: ${diasConTurnos}`);
            console.log(`- Días sin turnos (descanso): ${diasSinTurnos}`);
            console.log(`- Errores encontrados: ${errores.length}`);

            if (errores.length > 0) {
                console.log('Errores:', errores);
            }

            return { diasConTurnos, diasSinTurnos, errores };
        }

        // Función de validación legacy (mantener compatibilidad)
        function validarSecuenciaTurnos(año, mes) {
            return validarSecuenciaTurnos3x3(año, mes);
        }

        // Función para validar turnos desde la interfaz
        function validarTurnosActuales() {
            const validacion = validarSecuenciaTurnos(añoSeleccionado, mesSeleccionado);
            let mensaje = `Validación de turnos para ${new Date(añoSeleccionado, mesSeleccionado).toLocaleDateString('es-CL', {month: 'long', year: 'numeric'})}:\n\n`;
            mensaje += `📅 Días con turnos: ${validacion.diasConTurnos}\n`;
            mensaje += `😴 Días de descanso: ${validacion.diasSinTurnos}\n`;

            if (validacion.errores.length > 0) {
                mensaje += `\n⚠️ Errores encontrados (${validacion.errores.length}):\n`;
                mensaje += validacion.errores.slice(0, 5).join('\n');
                if (validacion.errores.length > 5) {
                    mensaje += `\n... y ${validacion.errores.length - 5} errores más.`;
                }
                mensaje += '\n\nRevisa la consola del navegador (F12) para ver todos los detalles.';
            } else {
                mensaje += '\n✅ No se encontraron errores en la secuencia de turnos.';
            }

            alert(mensaje);
        }

        // --- ESTADÍSTICAS Y CSV ---
        function calcularEstadisticas() { const stats = {}; for (let i = 1; i <= N_PERSONAS; i++) { stats[i] = { nombre: nombres[i] || `Persona ${i}`, dia: 0, noche: 0, extraDia: 0, extraNoche: 0, fijo: 0, horas: 0 }; } const diasMes = new Date(añoSeleccionado, mesSeleccionado + 1, 0).getDate(); for (let dia = 1; dia <= diasMes; dia++) { const turnosDia = turnos[añoSeleccionado]?.[mesSeleccionado]?.[dia] || {}; const diaSemana = new Date(añoSeleccionado, mesSeleccionado, dia).getDay(); for (const [id, data] of Object.entries(turnosDia)) { if (!stats[id]) continue; let horasTurno = 0; if (data.turno === 'fijo-1') { horasTurno = (diaSemana === 5) ? 7.5 : 8.5; stats[id].fijo++; } else if (data.turno === 'fijo-2') { horasTurno = (diaSemana === 5) ? 8 : 9; stats[id].fijo++; } else if (data.turno.includes('dia')) { horasTurno = 12; data.turno === 'dia' ? stats[id].dia++ : stats[id].extraDia++; } else if (data.turno.includes('noche')) { horasTurno = 12; data.turno === 'noche' ? stats[id].noche++ : stats[id].extraNoche++; } stats[id].horas += horasTurno; } } const table = document.getElementById('stats-table'); table.innerHTML = `<thead><tr><th>Personal</th><th>T. Día</th><th>T. Noche</th><th>T. Extra Día</th><th>T. Extra Noche</th><th>T. Fijos</th><th>Total Horas</th></tr></thead><tbody>`; for (let i = 1; i <= N_PERSONAS; i++) { table.innerHTML += `<tr><td>${stats[i].nombre}</td><td>${stats[i].dia}</td><td>${stats[i].noche}</td><td>${stats[i].extraDia}</td><td>${stats[i].extraNoche}</td><td>${stats[i].fijo}</td><td>${stats[i].horas.toFixed(2)}</td></tr>`; } table.innerHTML += `</tbody>`; return stats; }
        function exportarCSV() { const stats = calcularEstadisticas(); let csvContent = "data:text/csv;charset=utf-8,Personal,Turnos Dia,Turnos Noche,Turnos Extra Dia,Turnos Extra Noche,Turnos Fijos,Total Horas\n"; Object.values(stats).forEach(s => { csvContent += `${s.nombre.replace(/,/g, '')},${s.dia},${s.noche},${s.extraDia},${s.extraNoche},${s.fijo},${s.horas.toFixed(2)}\n`; }); const link = document.createElement("a"); link.setAttribute("href", encodeURI(csvContent)); link.setAttribute("download", `estadisticas_${mesSeleccionado+1}_${añoSeleccionado}.csv`); document.body.appendChild(link); link.click(); document.body.removeChild(link); }

        // --- HERRAMIENTAS: PDF, ICS, DATOS ---
        function exportarAPDF() {
            const startMonthInput = document.getElementById('export-start-month').value;
            const endMonthInput = document.getElementById('export-end-month').value;

            if (!startMonthInput || !endMonthInput) {
                return alert('Selecciona un rango de meses.');
            }

            const startDate = new Date(startMonthInput + '-02T00:00:00');
            const endDate = new Date(endMonthInput + '-02T00:00:00');

            if (startDate > endDate) {
                return alert('La fecha de inicio no puede ser posterior a la de fin.');
            }

            if (!window.jsPDF) {
                return alert('Error: La librería PDF no se ha cargado correctamente.');
            }

            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF({ orientation: 'landscape' });

                for (let d = new Date(startDate); d <= endDate; d.setMonth(d.getMonth() + 1)) {
                    if (d > startDate) doc.addPage('l');
                    const currentYear = d.getFullYear(), currentMonth = d.getMonth();
                    doc.setFontSize(18);
                    doc.text(`Calendario de Turnos - ${d.toLocaleDateString('es-CL', { month: 'long', year: 'numeric' })}`, doc.internal.pageSize.getWidth() / 2, 15, { align: 'center' });
                    const head = [[]], body = [];
                    const diasMes = new Date(currentYear, currentMonth + 1, 0).getDate();
                    head[0].push('Personal');
                    for (let i = 1; i <= diasMes; i++) { head[0].push(String(i)); }
                    for (let i = 1; i <= N_PERSONAS; i++) {
                        const personaRow = [nombres[i] || `Persona ${i}`];
                        for (let dia = 1; dia <= diasMes; dia++) {
                            const turno = turnos[currentYear]?.[currentMonth]?.[dia]?.[i]?.turno;
                            personaRow.push(turno ? TURNO_DEFINICIONES[turno].inicial : '');
                        }
                        body.push(personaRow);
                    }
                    doc.autoTable({
                        head, body, startY: 25, theme: 'grid',
                        styles:{fontSize: 8, cellPadding: 1},
                        headStyles: { fillColor: [25, 118, 210], halign: 'center' },
                        bodyStyles: { halign: 'center' },
                        didDrawCell: (data) => {
                            if (data.section === 'body' && data.cell.text[0]) {
                                const turnoKey = Object.keys(TURNO_DEFINICIONES).find(k => TURNO_DEFINICIONES[k].inicial === data.cell.text[0]);
                                if (turnoKey) {
                                    const color = TURNO_DEFINICIONES[turnoKey].color;
                                    doc.setFillColor(color);
                                    doc.rect(data.cell.x, data.cell.y, data.cell.width, data.cell.height, 'F');
                                    const textColor = TURNO_DEFINICIONES[turnoKey].texto;
                                    doc.setTextColor(textColor);
                                    doc.text(data.cell.text[0], data.cell.x + data.cell.width / 2, data.cell.y + data.cell.height / 2, { halign: 'center', valign: 'middle' });
                                }
                            }
                        }
                    });
                }
                doc.save(`calendario_turnos.pdf`);
            } catch (error) {
                console.error('Error generando PDF:', error);
                alert('Error al generar el PDF. Verifica que los datos estén correctos.');
            }
        }
        function exportarICS(personaId) { if (!window.ics) return alert('Error al cargar librería de exportación.'); const cal = ics(); const nombrePersona = nombres[personaId] || `Persona ${personaId}`; const diasMes = new Date(añoSeleccionado, mesSeleccionado + 1, 0).getDate(); for (let dia = 1; dia <= diasMes; dia++) { const turnoData = turnos[añoSeleccionado]?.[mesSeleccionado]?.[dia]?.[personaId]; if (!turnoData) continue; const fecha = new Date(añoSeleccionado, mesSeleccionado, dia); let start, end; let summary = `Turno: ${turnoData.turno.replace('-',' ')}`; if (turnoData.turno.includes('dia')) { start = new Date(fecha); start.setHours(8,0,0,0); end = new Date(fecha); end.setHours(20,0,0,0); } else if (turnoData.turno.includes('noche')) { start = new Date(fecha); start.setHours(20,0,0,0); const nextDay = new Date(fecha); nextDay.setDate(fecha.getDate() + 1); end = new Date(nextDay); end.setHours(8,0,0,0); } else if (turnoData.turno === 'fijo-1') { const esViernes = fecha.getDay() === 5; start = new Date(fecha); start.setHours(7,30,0,0); end = new Date(fecha); end.setHours(esViernes ? 15 : 16, 0, 0,0); } else if (turnoData.turno === 'fijo-2') { const esViernes = fecha.getDay() === 5; start = new Date(fecha); start.setHours(esViernes ? 12 : 11, 0, 0,0); end = new Date(fecha); end.setHours(20,0,0,0); } if (start && end) cal.addEvent(summary, `Turno para ${nombrePersona}`, '', start, end); } cal.download(`Horario ${nombrePersona} - ${mesSeleccionado+1}-${añoSeleccionado}`); }
        function guardarDatos() {
            try {
                localStorage.setItem('turnos_equipo', JSON.stringify(turnos));
                localStorage.setItem('notas_generales', JSON.stringify(notasGenerales));
            } catch (error) {
                console.error('Error guardando datos:', error);
                alert('Error al guardar los datos. Es posible que el almacenamiento local esté lleno.');
            }
        }
        function limpiarMes() { if (confirm(`¿Limpiar todos los turnos de ${document.getElementById('monthYear').textContent}?`)) { if (turnos[añoSeleccionado]?.[mesSeleccionado]) { delete turnos[añoSeleccionado][mesSeleccionado]; guardarDatos(); actualizarCalendario(); alert('Mes limpiado.'); } } }
        function limpiarTodosLosDatos() { if (confirm('¡ADVERTENCIA! ¿Estás absolutamente seguro de que quieres borrar TODOS los turnos de TODOS los meses? Esta acción no se puede deshacer.')) { if(confirm('Esta es tu última oportunidad. ¿Realmente quieres borrar toda la información de turnos guardada?')) { turnos = {}; notasGenerales = {}; guardarDatos(); actualizarCalendario(); alert('Todos los datos de turnos y notas han sido borrados.'); } } }
        function exportarDatosJSON() { const dataStr = JSON.stringify({turnos, nombres, notasGenerales}, null, 2); const blob = new Blob([dataStr], {type: "application/json"}); const a = document.createElement('a'); a.href = URL.createObjectURL(blob); a.download = `respaldo_turnos.json`; a.click(); URL.revokeObjectURL(a.href); }
        function importarDatosJSON() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (event) => {
                const file = event.target.files[0];
                if (!file) return;
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        if (confirm('Esto reemplazará todos los datos existentes. ¿Continuar?')) {
                            turnos = data.turnos || {};
                            nombres = data.nombres || {};
                            notasGenerales = data.notasGenerales || {};
                            guardarDatos();
                            localStorage.setItem('nombres_personas', JSON.stringify(nombres));
                            location.reload();
                        }
                    } catch (error) {
                        alert('Error al leer el archivo. Asegúrate de que sea un archivo JSON válido.');
                        console.error('Error parsing JSON:', error);
                    }
                };
                reader.readAsText(file);
            };
            input.click();
        }
        
        // --- INICIALIZACIÓN Y EVENTOS ---
        function cambiarMes(dir) { fechaActual.setMonth(fechaActual.getMonth() + dir); document.getElementById('monthSelect').value = fechaActual.getMonth(); document.getElementById('yearSelect').value = fechaActual.getFullYear(); actualizarCalendario(); }
        function cambiarPestaña(id) { document.querySelectorAll('.tab-content, .tab, .mobile-nav-item').forEach(el => el.classList.remove('active')); document.getElementById('tab-' + id).classList.add('active'); document.querySelector(`.tab[onclick*="'${id}'"]`)?.classList.add('active'); document.querySelector(`.mobile-nav-item[onclick*="'${id}'"]`)?.classList.add('active'); if (id === 'estadisticas') { calcularEstadisticas(); } }
        function cambiarTema() { document.body.classList.toggle('dark-mode'); const esDark = document.body.classList.contains('dark-mode'); localStorage.setItem('tema_equipo', esDark ? 'oscuro' : 'claro'); document.getElementById('theme-icon').className = `fas ${esDark ? 'fa-sun' : 'fa-moon'} mr-2`; }

        document.addEventListener('DOMContentLoaded', async () => {
            // Cargar datos del localStorage de forma segura
            cargarDatosLocalStorage();

            if (localStorage.getItem('tema_equipo') === 'oscuro') { document.body.classList.add('dark-mode'); }
            document.getElementById('theme-icon').className = `fas ${document.body.classList.contains('dark-mode') ? 'fa-sun' : 'fa-moon'} mr-2`;
            const monthSelect = document.getElementById('monthSelect'), yearSelect = document.getElementById('yearSelect');
            monthSelect.innerHTML = ["Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio", "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"].map((m, i) => `<option value="${i}">${m}</option>`).join('');
            const currentYear = new Date().getFullYear(); for (let i = currentYear - 2; i <= currentYear + 3; i++) { yearSelect.innerHTML += `<option value="${i}">${i}</option>`; }
            monthSelect.value = fechaActual.getMonth(); yearSelect.value = fechaActual.getFullYear();
            
            document.getElementById('calendar-header').innerHTML = ['Lu', 'Ma', 'Mi', 'Ju', 'Vi', 'Sa', 'Do'].map(d => `<div class="day-header">${d}</div>`).join('');
            
            document.getElementById('editDayButton').addEventListener('click', (event) => {
                const dia = event.currentTarget.dataset.dia;
                if (dia) {
                    cerrarModal('daySummaryModal');
                    setTimeout(() => abrirModal(parseInt(dia)), 50);
                }
            });
            
            document.getElementById('add-turno-select').innerHTML = `<option value="libre" disabled>Selecciona turno</option><option value="dia">Día (12h)</option><option value="noche">Noche (12h)</option><option value="extra-dia">Extra Día (12h)</option><option value="extra-noche">Extra Noche (12h)</option><option value="fijo-1">Fijo 7</option><option value="fijo-2">Fijo 8</option>`;

            document.getElementById('generador-equipo-referencia').addEventListener('change', function() {
                const equipo = this.value;
                const personaSelect = document.getElementById('generador-persona-dia-referencia');
                personaSelect.innerHTML = '';
                const equipoMiembros = equipo === 'A' ? [1,2,3] : [4,5,6];
                equipoMiembros.forEach(id => { personaSelect.innerHTML += `<option value="${id}">${nombres[id] || `Persona ${id}`}</option>`; });
            });

            cargarNombres();
            actualizarLeyendas();
            actualizarSelectoresDePersona();
            document.getElementById('generador-equipo-referencia').dispatchEvent(new Event('change'));
            await actualizarCalendario();
            cambiarPestaña('calendario');
        });
    </script>
</body>
</html>